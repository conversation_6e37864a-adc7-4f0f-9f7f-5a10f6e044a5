"""
Trade checker script for finding high-quality trading opportunities.
This runs independently from the Telegram bot to ensure responsiveness.
"""
import os
import sys
import time
import uuid
import asyncio
import logging
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Set up logging
from config.logging_config_consolidated import configure_logging
logger = configure_logging()
logger = logger.getChild(__name__)

# Import services
from services.binance_service import ExchangeService
from services.trading_strategy import TradingStrategy
from services.signal_service import TradeSignal

# Import shared data manager
from utils.shared_data_manager import (
    update_bot_status, update_waiting_trades, update_active_trades,
    update_trade_stats, get_pending_commands, mark_command_processed,
    add_waiting_trade, get_waiting_trades, load_shared_data, save_shared_data,
    add_command
)

# Import terminal display for local monitoring
from utils.terminal_display_consolidated import display_terminal

class TradeChecker:
    """Service for checking trades and finding high-quality opportunities."""

    def __init__(self):
        """Initialize the trade checker."""
        self.exchange = ExchangeService()
        self.trading_strategy = TradingStrategy(self.exchange)
        self.running = False

        # Trade statistics
        self.trade_stats = {
            'success_spot': 0,
            'success_future': 0,
            'failed_spot': 0,
            'failed_future': 0
        }

        # Current state
        self.current_pair = ""
        self.current_market = ""
        self.current_conditions = {}

        # Initialize idle mode variables
        self.idle_mode = False
        self.idle_market_type = None
        self.idle_user_id = None

        # Waiting trades
        self.waiting_spot = []
        self.waiting_future = []

        # Active trades
        self.active_spot = {}
        self.active_future = {}

        # Trade quality thresholds - STRICT SMC/ICT ONLY
        self.min_quality_score = 0.95  # Very high threshold for premium setups only
        self.min_concepts_required = 3  # Minimum number of SMC/ICT concepts that must align
        self.min_wait_time = 60  # Minimum time (seconds) to wait between finding trades
        self.last_trade_time = datetime.now() - timedelta(seconds=self.min_wait_time)

        # Load existing data
        self._load_existing_data()

    def _load_existing_data(self):
        """Load existing data from shared files."""
        # Load shared data
        shared_data = load_shared_data()

        # Load trade stats
        self.trade_stats['success_spot'] = shared_data["trade_stats"]["success_spot"]
        self.trade_stats['success_future'] = shared_data["trade_stats"]["success_future"]
        self.trade_stats['failed_spot'] = shared_data["trade_stats"]["failed_spot"]
        self.trade_stats['failed_future'] = shared_data["trade_stats"]["failed_future"]

        # Load waiting trades
        self.waiting_spot = shared_data["waiting_trades"]["spot"]
        self.waiting_future = shared_data["waiting_trades"]["futures"]

        # Load active trades
        self.active_spot = shared_data["active_trades"]["spot"]
        self.active_future = shared_data["active_trades"]["futures"]

    async def start(self):
        """Start the trade checker."""
        if self.running:
            logger.warning("Trade checker already running")
            return

        self.running = True
        logger.info("Starting trade checker")

        # Set idle mode to true by default - only check trades when requested
        self.idle_mode = True

        # Update bot status
        update_bot_status({
            "is_running": True,
            "current_pair": "",
            "current_market": "",
            "current_conditions": {"status": "Idle - Waiting for trade requests"}
        })

        # Set a flag to prevent recursive error handling
        error_recovery_in_progress = False

        while self.running:
            try:
                # Process any pending commands
                await self._process_commands()

                # If in idle mode, just update status and sleep
                if self.idle_mode:
                    # Update shared data
                    self._update_shared_data()

                    # Sleep for a while
                    await asyncio.sleep(1)
                    continue

                # Normal operation mode
                # Check market conditions
                viable_pairs = await self._check_market()

                # Check for trade opportunities
                await self._check_for_trades(viable_pairs)

                # Check active trades
                await self._check_active_trades()

                # Update shared data
                self._update_shared_data()

                # Reset error recovery flag after successful execution
                error_recovery_in_progress = False

                # Sleep for a while to reduce system load
                await asyncio.sleep(5)  # Check every 5 seconds
            except Exception as e:
                # Prevent recursive error handling
                if error_recovery_in_progress:
                    logger.critical(f"Error during error recovery: {e}")
                    # Force a longer sleep to break potential recursion
                    await asyncio.sleep(300)  # 5 minutes
                else:
                    error_recovery_in_progress = True
                    logger.error(f"Error in trade checker: {e}")
                    await asyncio.sleep(60)  # Sleep and retry

    def stop(self):
        """Stop the trade checker."""
        self.running = False
        logger.info("Stopping trade checker")

        # Update bot status
        update_bot_status({
            "is_running": False
        })

    async def _process_commands(self):
        """Process any pending commands."""
        commands = get_pending_commands()

        for command in commands:
            command_type = command.get("type")
            command_id = command.get("id")

            if command_type == "stop":
                self.stop()
            elif command_type == "restart":
                self.stop()
                await asyncio.sleep(1)
                await self.start()
            elif command_type == "clear_waiting_trades":
                self.waiting_spot = []
                self.waiting_future = []
                update_waiting_trades(self.waiting_spot, self.waiting_future)
            elif command_type == "find_trade":
                # Get market type and user ID from command
                market_type = command.get("market_type", "SPOT")
                user_id = command.get("user_id")

                # Log the request
                logger.info(f"Processing find_trade command for {market_type} market requested by user {user_id}")

                # Process the trade request immediately
                # No need to track idle mode or queue - just process each request directly

                # Update bot status
                update_bot_status({
                    "is_running": True,
                    "current_pair": "",
                    "current_market": market_type,
                    "current_conditions": {"status": f"Searching for high-quality {market_type} trade for user {user_id}..."}
                })

                # Find a high-quality trade for this user
                await self._find_specific_trade(market_type, user_id)



            # Mark command as processed
            mark_command_processed(command_id)

    async def _check_market(self):
        """Check market conditions and get viable trading pairs."""
        logger.info("Checking market conditions")

        try:
            # Update bot status
            update_bot_status({
                "current_pair": "",
                "current_market": "",
                "current_conditions": {"status": "Getting viable trading pairs..."}
            })

            # Get viable trading pairs
            viable_pairs = self.exchange.get_viable_trading_pairs()

            # Limit to top 150 tokens to avoid overload
            if len(viable_pairs) > 150:
                logger.info(f"Limiting check to top 150 tokens out of {len(viable_pairs)}")
                viable_pairs = viable_pairs[:150]

            # Update display
            display_terminal()

            return viable_pairs
        except Exception as e:
            logger.error(f"Error checking market: {e}")
            return []

    async def _check_for_trades(self, viable_pairs):
        """Check for trade opportunities with improved quality assessment."""
        logger.info("Checking for trade opportunities...")

        # Filter out stable coin pairs and pairs already in waiting list
        filtered_pairs = []
        for pair in viable_pairs:
            # Skip stable coin pairs
            if self._is_stable_coin_pair(pair):
                continue

            # Skip pairs already in waiting list
            if pair in [trade["symbol"] for trade in self.waiting_spot]:
                continue
            if pair in [trade["symbol"] for trade in self.waiting_future]:
                continue

            filtered_pairs.append(pair)

        # Shuffle pairs to avoid always checking the same ones first
        random.shuffle(filtered_pairs)

        # Check both SPOT and FUTURES markets simultaneously
        await self._check_market_pairs(filtered_pairs)

        # Update shared data
        self._update_shared_data()

    async def _check_market_pairs(self, viable_pairs):
        """Check both SPOT and FUTURES markets simultaneously."""
        # Filter out stable coin pairs
        filtered_pairs = [p for p in viable_pairs if not self._is_stable_coin_pair(p)]

        # Process pairs in batches to avoid overwhelming the system
        batch_size = 5  # Process 5 pairs at a time for each market

        for i in range(0, len(filtered_pairs), batch_size):
            batch = filtered_pairs[i:i+batch_size]

            # Create spot market tasks
            spot_tasks = []
            for symbol in batch:
                spot_tasks.append(self._analyze_pair(symbol, "SPOT"))

            # Create futures market tasks
            future_tasks = []
            for symbol in batch:
                future_tasks.append(self._analyze_pair(symbol, "FUTURES"))

            # Run spot and futures tasks concurrently
            await asyncio.gather(*spot_tasks, *future_tasks)

            # Add a small delay between batches to keep the system responsive
            await asyncio.sleep(1.0)

            # Update display
            display_terminal()

    async def _analyze_pair(self, symbol, market_type):
        """Analyze a single trading pair for a specific market type with quality assessment."""
        # Initialize conditions
        conditions = {
            'ICT': 'checking...',
            'SMC': 'checking...',
            'PA': 'checking...',
            'S/R': 'checking...',
            'Pattern recognition': 'checking...'
        }

        # Update current pair and market
        self.current_pair = symbol
        self.current_market = market_type
        self.current_conditions = conditions.copy()

        # Update bot status
        update_bot_status({
            "current_pair": symbol,
            "current_market": market_type,
            "current_conditions": self.current_conditions
        })

        try:
            # Analyze the symbol with specific market type
            analysis_result = self.trading_strategy.analyze_symbol(symbol, market_type)

            # If analysis_result is None, skip this symbol for this market type
            if analysis_result is None:
                logger.info(f"No valid analysis for {symbol} in {market_type} market")
                return

            # Update conditions based on analysis
            for condition, result in analysis_result.items():
                # Skip non-condition keys
                if condition in ["symbol", "market_type"]:
                    continue

                if result:
                    conditions[condition] = "valid ✅"
                else:
                    conditions[condition] = "not valid ❌"

            # Update current conditions
            self.current_conditions = conditions.copy()

            # Update bot status
            update_bot_status({
                "current_conditions": self.current_conditions
            })

            # Check if all conditions are met and we have a valid direction
            all_conditions_met = True
            for key, value in analysis_result.items():
                if key not in ["symbol", "market_type", "direction", "explanations", "concepts_met"] and not value:
                    all_conditions_met = False
                    break

            # Get the signal direction
            signal_direction = analysis_result.get("direction", "NONE")

            if all_conditions_met and signal_direction in ["BUY", "SELL"]:
                # Calculate quality score (0-1)
                quality_score = self._calculate_quality_score(symbol, market_type)

                # Check if quality score meets minimum threshold
                if quality_score >= self.min_quality_score:
                    # Check if enough time has passed since last trade
                    time_since_last_trade = (datetime.now() - self.last_trade_time).total_seconds()
                    if time_since_last_trade >= self.min_wait_time:
                        # Create enhanced trade signal with detailed information
                        try:
                            volatility = self.trading_strategy.calculate_volatility(symbol)
                        except:
                            volatility = 0.03  # Default volatility

                        # Generate leverage recommendation for futures
                        leverage_rec = ""
                        if market_type == "FUTURES":
                            if volatility < 0.02:
                                leverage_rec = "5-10x"
                            elif volatility < 0.04:
                                leverage_rec = "3-5x"
                            elif volatility < 0.06:
                                leverage_rec = "2-3x"
                            else:
                                leverage_rec = "1-2x"

                        # Generate detailed explanations
                        explanations = analysis_result.get("explanations", [])
                        concepts = analysis_result.get("concepts_met", [])

                        # Create market structure analysis
                        market_structure = ""
                        if signal_direction == "BUY":
                            market_structure = "Bullish market structure confirmed with higher highs and higher lows pattern"
                        else:
                            market_structure = "Bearish market structure confirmed with lower highs and lower lows pattern"

                        # Create volume analysis
                        volume_analysis = "Institutional volume patterns support the directional bias with smart money accumulation/distribution"

                        # Create risk assessment
                        risk_assessment = f"High-probability setup with {quality_score:.1%} confidence rating and optimal risk/reward ratio"

                        signal = TradeSignal(
                            symbol=symbol,
                            direction=signal_direction,
                            entry_price=self.trading_strategy.get_current_price(symbol),
                            stop_loss=self.trading_strategy.calculate_stop_loss(symbol, signal_direction),
                            targets=self.trading_strategy.calculate_targets(symbol, signal_direction),
                            win_rate=0.90,  # High win rate for signals that meet all conditions
                            market_type=market_type,
                            technical_analysis=explanations,
                            concepts_met=concepts,
                            market_structure=market_structure,
                            volume_analysis=volume_analysis,
                            risk_assessment=risk_assessment,
                            quality_score=quality_score,
                            volatility=volatility,
                            leverage_recommendation=leverage_rec
                        )

                        # Create trade details
                        trade_details = {
                            "symbol": symbol,
                            "market_type": market_type,
                            "direction": signal_direction,  # Use determined direction
                            "entry_price": self.trading_strategy.get_current_price(symbol),
                            "stop_loss": self.trading_strategy.calculate_stop_loss(symbol, signal_direction),
                            "targets": self.trading_strategy.calculate_targets(symbol, signal_direction),
                            "quality_score": quality_score,
                            "found_at": datetime.now().isoformat(),
                            "conditions": {k: v for k, v in conditions.items()},
                            "explanations": analysis_result.get("explanations", []),
                            "concepts_met": analysis_result.get("concepts_met", [])
                        }

                        # Add to waiting trades
                        if market_type == "SPOT":
                            self.waiting_spot.append(trade_details)
                        else:
                            self.waiting_future.append(trade_details)

                        # Add to shared data
                        add_waiting_trade(trade_details, market_type)

                        # Update last trade time
                        self.last_trade_time = datetime.now()

                        # Log the high-quality trade
                        logger.info(f"Found high-quality {market_type} trade: {symbol} (Score: {quality_score:.2f})")
                    else:
                        logger.info(f"Found good {market_type} trade: {symbol}, but waiting for minimum time between trades")
                else:
                    logger.info(f"Found {market_type} trade: {symbol}, but quality score ({quality_score:.2f}) below threshold")
        except Exception as e:
            logger.error(f"Error analyzing {symbol} for {market_type}: {e}")

    def _calculate_quality_score(self, symbol, market_type):
        """
        Calculate a quality score (0-1) based PURELY on SMC/ICT concepts.
        NO risk/reward ratios - only institutional trading concepts.
        """
        try:
            # Get analysis from trading strategy
            analysis_result = self.trading_strategy.analyze_symbol(symbol, market_type)

            if not analysis_result:
                return 0.0

            # Get the concepts that were met
            concepts_met = analysis_result.get("concepts_met", [])
            explanations = analysis_result.get("explanations", [])
            direction = analysis_result.get("direction", "NONE")

            # If no clear direction, score is 0
            if direction == "NONE":
                return 0.0

            # Base score starts at 0
            quality_score = 0.0

            # SMC/ICT Concept Scoring (each concept adds significant value)
            concept_scores = {
                "ICT": 0.25,    # Fair Value Gaps, Liquidity Sweeps, Order Blocks
                "SMC": 0.25,    # Break of Structure, Market Structure
                "PA": 0.15,     # Price Action patterns
                "S/R": 0.15,    # Support/Resistance levels
                "Pattern Recognition": 0.10  # Chart patterns
            }

            # Add scores for each concept met
            for concept in concepts_met:
                if concept in concept_scores:
                    quality_score += concept_scores[concept]

            # Confluence Bonus: Multiple concepts = higher quality
            if len(concepts_met) >= 4:
                quality_score += 0.15  # 4+ concepts = premium setup
            elif len(concepts_met) >= 3:
                quality_score += 0.10  # 3 concepts = good setup

            # High-Impact SMC/ICT Pattern Bonuses
            combined_explanations = " ".join(explanations).lower()

            # Premium ICT patterns (these are the highest quality setups)
            premium_patterns = {
                "fair value gap": 0.05,
                "liquidity sweep": 0.05,
                "order block": 0.05,
                "break of structure": 0.05,
                "institutional": 0.03,
                "smart money": 0.03,
                "imbalance": 0.03
            }

            for pattern, bonus in premium_patterns.items():
                if pattern in combined_explanations:
                    quality_score += bonus

            # Market Structure Quality Indicators
            structure_indicators = {
                "higher highs": 0.02,
                "higher lows": 0.02,
                "lower highs": 0.02,
                "lower lows": 0.02,
                "swing": 0.02,
                "demand zone": 0.03,
                "supply zone": 0.03
            }

            for indicator, bonus in structure_indicators.items():
                if indicator in combined_explanations:
                    quality_score += bonus

            # Volume Analysis Bonus (institutional footprints)
            if "high volume" in combined_explanations or "volume" in combined_explanations:
                quality_score += 0.03

            # Ensure score doesn't exceed 1.0
            quality_score = min(1.0, quality_score)

            # Final requirement: Must have minimum concepts for any score above 0.5
            if len(concepts_met) < self.min_concepts_required and quality_score > 0.5:
                quality_score = 0.5

            return quality_score

        except Exception as e:
            logger.error(f"Error calculating SMC/ICT quality score for {symbol}: {e}")
            return 0.0  # Return 0 for any errors

    async def _check_active_trades(self):
        """Check active trades and update their status."""
        logger.info("Checking active trades...")

        # Check spot trades
        for symbol, trade in list(self.active_spot.items()):
            try:
                # Get current price
                current_price = self.trading_strategy.get_current_price(symbol)

                # Skip if price couldn't be fetched
                if current_price == 0:
                    continue

                # Check if stop loss hit
                if trade["direction"] == "BUY" and current_price <= trade["stop_loss"]:
                    # Mark stop loss as hit
                    trade["stop_loss_hit"] = True
                    trade["exit_price"] = current_price
                    trade["exit_time"] = datetime.now().isoformat()

                    # Move to failed trades
                    self.trade_stats["failed_spot"] += 1

                    # Remove from active trades
                    del self.active_spot[symbol]

                    logger.info(f"Stop loss hit for {symbol} at {current_price}")
                    continue

                # Check if targets hit
                for i, target in enumerate(trade["targets"]):
                    if i not in trade["targets_hit"] and trade["direction"] == "BUY" and current_price >= target:
                        # Mark target as hit
                        trade["targets_hit"].append(i)

                        logger.info(f"Target {i+1} hit for {symbol} at {current_price}")

                # Check if all targets hit
                if len(trade["targets_hit"]) == len(trade["targets"]):
                    # Move to successful trades
                    self.trade_stats["success_spot"] += 1

                    # Remove from active trades
                    del self.active_spot[symbol]

                    logger.info(f"All targets hit for {symbol}")
            except Exception as e:
                logger.error(f"Error checking active spot trade {symbol}: {e}")

        # Check futures trades
        for symbol, trade in list(self.active_future.items()):
            try:
                # Get current price
                current_price = self.trading_strategy.get_current_price(symbol)

                # Skip if price couldn't be fetched
                if current_price == 0:
                    continue

                # Check if stop loss hit
                if trade["direction"] == "BUY" and current_price <= trade["stop_loss"]:
                    # Mark stop loss as hit
                    trade["stop_loss_hit"] = True
                    trade["exit_price"] = current_price
                    trade["exit_time"] = datetime.now().isoformat()

                    # Move to failed trades
                    self.trade_stats["failed_future"] += 1

                    # Remove from active trades
                    del self.active_future[symbol]

                    logger.info(f"Stop loss hit for futures {symbol} at {current_price}")
                    continue

                # Check if targets hit
                for i, target in enumerate(trade["targets"]):
                    if i not in trade["targets_hit"] and trade["direction"] == "BUY" and current_price >= target:
                        # Mark target as hit
                        trade["targets_hit"].append(i)

                        logger.info(f"Target {i+1} hit for futures {symbol} at {current_price}")

                # Check if all targets hit
                if len(trade["targets_hit"]) == len(trade["targets"]):
                    # Move to successful trades
                    self.trade_stats["success_future"] += 1

                    # Remove from active trades
                    del self.active_future[symbol]

                    logger.info(f"All targets hit for futures {symbol}")
            except Exception as e:
                logger.error(f"Error checking active futures trade {symbol}: {e}")

        # Update shared data
        self._update_shared_data()

    def _update_shared_data(self):
        """Update shared data with current state."""
        # Update waiting trades
        update_waiting_trades(self.waiting_spot, self.waiting_future)

        # Update active trades
        update_active_trades(self.active_spot, self.active_future)

        # Update trade statistics
        update_trade_stats(
            self.trade_stats["success_spot"],
            self.trade_stats["success_future"],
            self.trade_stats["failed_spot"],
            self.trade_stats["failed_future"]
        )

        # Update bot status
        update_bot_status({
            "is_running": self.running,
            "current_pair": self.current_pair,
            "current_market": self.current_market,
            "current_conditions": self.current_conditions
        })

    def _is_stable_coin_pair(self, pair):
        """Check if a pair is a stable coin pair that should be skipped."""
        stable_coins = ["USDC", "BUSD", "TUSD", "DAI", "USDT", "FDUSD", "USDK", "USDP", "USDD"]

        # Extract the base currency from the pair (e.g., "BTC" from "BTC/USDT")
        if "/" in pair:
            base = pair.split("/")[0]
            quote = pair.split("/")[1]

            # Check if both base and quote are stable coins
            return base in stable_coins and quote in stable_coins

        return False

    def _generate_trade_explanation(self, trade_data):
        """Generate an explanation for why this trade was selected."""
        symbol = trade_data["symbol"]
        direction = trade_data["direction"]

        # Use real explanations from analysis if available
        real_explanations = trade_data.get("explanations", [])
        concepts_met = trade_data.get("concepts_met", [])

        explanation = []

        # Add the real technical analysis explanations
        if real_explanations:
            explanation.append("*Technical Analysis Findings:*")
            for exp in real_explanations:
                # Format each explanation as a bullet point
                if not exp.startswith("•"):
                    explanation.append(f"• {exp}")
                else:
                    explanation.append(exp)

        # Add concepts met summary
        if concepts_met:
            explanation.append(f"\n*Trading Concepts Confirmed:* {', '.join(concepts_met)}")

        # Add directional bias explanation
        if direction == "BUY":
            explanation.append("\n*Bullish Bias Confirmed:*")
            explanation.append("• Multiple bullish signals aligned")
            explanation.append("• Price structure supports upward movement")
            explanation.append("• Risk/reward ratio favors long position")
        elif direction == "SELL":
            explanation.append("\n*Bearish Bias Confirmed:*")
            explanation.append("• Multiple bearish signals aligned")
            explanation.append("• Price structure supports downward movement")
            explanation.append("• Risk/reward ratio favors short position")

        # Add market context
        try:
            # Add market-specific explanation
            if "btc" in symbol.lower():
                explanation.append("• Bitcoin - Market leader with high institutional interest")
            elif "eth" in symbol.lower():
                explanation.append("• Ethereum - Strong fundamentals and DeFi ecosystem")
            elif "usdt" in symbol.lower():
                explanation.append("• USDT pair offers good stability and liquidity")

            # Add volatility context if available
            try:
                volatility = self.trading_strategy.calculate_volatility(symbol)
                if volatility > 0.05:  # 5% volatility
                    explanation.append(f"• Higher volatility ({volatility:.1%}) offers good profit potential")
                else:
                    explanation.append(f"• Lower volatility ({volatility:.1%}) reduces risk")
            except:
                pass

        except Exception as e:
            logger.error(f"Error generating market context for explanation: {e}")

        # If no explanations were provided, add generic ones
        if not real_explanations and not concepts_met:
            explanation.append("• Multiple technical factors aligned for this trade")
            explanation.append("• High probability setup based on our analysis")

        # Return the formatted explanation
        return "\n".join(explanation)

    async def _find_specific_trade(self, market_type, user_id):
        """Find a high-quality trade for a specific user and market type."""
        from telegram import Bot
        from config.settings import BOT_TOKEN

        logger.info(f"Finding a high-quality {market_type} trade for user {user_id}")

        # Create a bot instance to send messages
        bot = Bot(token=BOT_TOKEN)

        # Get viable trading pairs
        viable_pairs = self.exchange.get_viable_trading_pairs()

        # Limit to top 150 tokens to avoid overload
        if len(viable_pairs) > 150:
            viable_pairs = viable_pairs[:150]

        # Filter out stable coin pairs
        filtered_pairs = [p for p in viable_pairs if not self._is_stable_coin_pair(p)]

        # Shuffle pairs to avoid always checking the same ones first
        random.shuffle(filtered_pairs)

        # Send initial progress message that we'll update
        progress_message = None
        try:
            progress_message = await bot.send_message(
                chat_id=user_id,
                text=f"▶ *SINQ TRADE SCANNER INITIATED*\n\n"
                     f"• *Objective:* Identify premium {market_type} opportunities\n"
                     f"• *Scope:* {len(filtered_pairs)} trading pairs under analysis\n"
                     f"• *Engine:* Multi-timeframe technical analysis\n\n"
                     f"*Progress:* 0.0% [░░░░░░░░░░]\n"
                     f"*Status:* Initializing scanner engine\n"
                     f"*Phase:* System startup and pair filtering",
                parse_mode='Markdown'
            )
        except Exception as e:
            logger.error(f"Error sending initial progress message to user {user_id}: {e}")
            # Try to continue even if we couldn't send the initial message

        # Track the best trade found
        best_trade = None
        best_quality = 0

        # Process pairs in smaller batches with progress updates
        batch_size = 10
        total_batches = (len(filtered_pairs) + batch_size - 1) // batch_size

        # For slower, more visible updates
        update_frequency = 2  # Update every 2 batches

        for batch_num, i in enumerate(range(0, len(filtered_pairs), batch_size)):
            batch = filtered_pairs[i:i+batch_size]

            # Update progress message every few batches
            if batch_num % update_frequency == 0:
                progress = (batch_num / total_batches) * 100

                # Create visual progress bar
                progress_blocks = int(progress / 10)  # 10 blocks total
                progress_bar = "█" * progress_blocks + "░" * (10 - progress_blocks)

                # Determine current analysis phase
                if progress < 25:
                    phase = "Analyzing market structure patterns"
                    status = "Evaluating price action and trend analysis"
                elif progress < 50:
                    phase = "Evaluating volume distribution"
                    status = "Analyzing institutional footprints"
                elif progress < 75:
                    phase = "Computing risk/reward metrics"
                    status = "Calculating optimal entry and exit points"
                else:
                    phase = "Finalizing trade selection"
                    status = "Ranking opportunities by quality score"

                try:
                    if progress_message:
                        await bot.edit_message_text(
                            chat_id=user_id,
                            message_id=progress_message.message_id,
                            text=f"▶ *SINQ TRADE SCANNER INITIATED*\n\n"
                                 f"• *Objective:* Identify premium {market_type} opportunities\n"
                                 f"• *Scope:* {len(filtered_pairs)} trading pairs under analysis\n"
                                 f"• *Engine:* Multi-timeframe technical analysis\n\n"
                                 f"*Progress:* {progress:.1f}% [{progress_bar}]\n"
                                 f"*Status:* {phase}\n"
                                 f"*Phase:* {status}",
                            parse_mode='Markdown'
                        )
                except Exception as e:
                    logger.error(f"Error updating progress message for user {user_id}: {e}")

            # Analyze each pair in the batch
            for symbol in batch:
                # Update current pair and market
                self.current_pair = symbol
                self.current_market = market_type

                # Update bot status
                update_bot_status({
                    "current_pair": symbol,
                    "current_market": market_type,
                    "current_conditions": {"status": f"Analyzing {symbol} for {market_type} market..."}
                })

                # Analyze the pair
                try:
                    # Analyze the symbol with specific market type
                    analysis_result = self.trading_strategy.analyze_symbol(symbol, market_type)

                    # Skip if no valid analysis
                    if analysis_result is None:
                        continue

                    # Check if all conditions are met and we have a valid direction
                    all_conditions_met = True
                    for key, value in analysis_result.items():
                        if key not in ["symbol", "market_type", "direction", "explanations", "concepts_met"] and not value:
                            all_conditions_met = False
                            break

                    # Get the signal direction
                    signal_direction = analysis_result.get("direction", "NONE")

                    if all_conditions_met and signal_direction in ["BUY", "SELL"]:
                        # Calculate quality score
                        quality_score = self._calculate_quality_score(symbol, market_type)

                        # Check if this is the best trade so far
                        if quality_score > best_quality:
                            best_quality = quality_score

                            # Create trade details
                            best_trade = {
                                "symbol": symbol,
                                "market_type": market_type,
                                "direction": signal_direction,  # Use determined direction
                                "entry_price": self.trading_strategy.get_current_price(symbol),
                                "stop_loss": self.trading_strategy.calculate_stop_loss(symbol, signal_direction),
                                "targets": self.trading_strategy.calculate_targets(symbol, signal_direction),
                                "quality_score": quality_score,
                                "found_at": datetime.now().isoformat(),
                                "conditions": {k: v for k, v in analysis_result.items() if k not in ["symbol", "market_type", "direction", "explanations", "concepts_met"]},
                                "explanations": analysis_result.get("explanations", []),
                                "concepts_met": analysis_result.get("concepts_met", [])
                            }

                            logger.info(f"Found potential {market_type} trade: {symbol} (Score: {quality_score:.2f})")
                except Exception as e:
                    logger.error(f"Error analyzing {symbol} for {market_type}: {e}")

            # Add a small delay between batches
            await asyncio.sleep(1.0)

        # Delete the progress message
        try:
            if progress_message:
                await bot.delete_message(chat_id=user_id, message_id=progress_message.message_id)
        except Exception as e:
            logger.error(f"Error deleting progress message: {e}")

        # Check if we found a good trade
        if best_trade and best_quality >= self.min_quality_score:
            logger.info(f"Found high-quality {market_type} trade for user {user_id}: {best_trade['symbol']} (Score: {best_quality:.2f})")

            # Add to waiting trades
            if market_type == "SPOT":
                self.waiting_spot.append(best_trade)
            else:
                self.waiting_future.append(best_trade)

            # Add to shared data
            add_waiting_trade(best_trade, market_type)

            # Format trade message
            entry_price = best_trade["entry_price"]
            stop_loss = best_trade["stop_loss"]
            targets = best_trade["targets"]

            # SMC/ICT targets based on market structure - no R/R ratios needed

            # Format prices with appropriate precision
            if entry_price < 0.1:
                price_format = "{:.8f}"
            elif entry_price < 1:
                price_format = "{:.6f}"
            elif entry_price < 100:
                price_format = "{:.4f}"
            else:
                price_format = "{:.2f}"

            # Generate detailed trade explanation based on conditions
            explanation = self._generate_trade_explanation(best_trade)

            # Create message
            message = (
                f"🎯 *HIGH-QUALITY {market_type} TRADE FOUND*\n\n"
                f"*Symbol:* {best_trade['symbol']}\n"
                f"*Direction:* {best_trade['direction']}\n"
                f"*Entry Price:* {price_format.format(entry_price)}\n"
                f"*Stop Loss:* {price_format.format(stop_loss)}\n\n"
                f"*Targets:*\n"
            )

            for i, target in enumerate(targets):
                message += f"{i+1}. {price_format.format(target)}\n"

            message += f"\n*Quality Score:* {best_quality:.2f}/1.0\n\n"

            # Add detailed explanation for why this trade was picked
            message += f"*Why This Trade Was Selected:*\n"

            # Add specific pattern recognition details
            if best_trade["conditions"].get("Pattern condition", False):
                if best_trade['direction'] == "BUY":
                    message += f"• Bullish pattern detected with clear break of structure (BOS)\n"
                    message += f"• Price broke above key resistance level\n"
                else:
                    message += f"• Bearish pattern detected with clear break of structure (BOS)\n"
                    message += f"• Price broke below key support level\n"

            # Add support/resistance details
            if best_trade["conditions"].get("Support/Resistance condition", False):
                if best_trade['direction'] == "BUY":
                    message += f"• Price found strong support at key level\n"
                    message += f"• Multiple timeframe support confluence\n"
                else:
                    message += f"• Price rejected from strong resistance\n"
                    message += f"• Multiple timeframe resistance confluence\n"

            # Add trend details
            if best_trade["conditions"].get("Trend condition", False):
                if best_trade['direction'] == "BUY":
                    message += f"• Strong uptrend confirmed on multiple timeframes\n"
                    message += f"• Higher highs and higher lows pattern\n"
                else:
                    message += f"• Strong downtrend confirmed on multiple timeframes\n"
                    message += f"• Lower highs and lower lows pattern\n"

            # Add volume details
            if best_trade["conditions"].get("Volume condition", False):
                if best_trade['direction'] == "BUY":
                    message += f"• Increasing buy volume confirms bullish momentum\n"
                else:
                    message += f"• Increasing sell volume confirms bearish pressure\n"

            # Add momentum details
            if best_trade["conditions"].get("Momentum condition", False):
                if best_trade['direction'] == "BUY":
                    message += f"• Strong bullish momentum on indicators\n"
                else:
                    message += f"• Strong bearish momentum on indicators\n"

            # Add the detailed explanation
            message += f"\n*Technical Analysis Details:*\n{explanation}\n"

            # Add specific guidance based on market type
            if market_type == "FUTURES":
                # Calculate recommended leverage based on volatility and risk
                try:
                    volatility = self.trading_strategy.calculate_volatility(best_trade['symbol'])
                    # Lower volatility allows for higher leverage, higher volatility requires lower leverage
                    if volatility < 0.02:  # Very low volatility (<2%)
                        recommended_leverage = "5-10x"
                    elif volatility < 0.04:  # Low volatility (2-4%)
                        recommended_leverage = "3-5x"
                    elif volatility < 0.06:  # Medium volatility (4-6%)
                        recommended_leverage = "2-3x"
                    else:  # High volatility (>6%)
                        recommended_leverage = "1-2x"
                except:
                    # Default recommendation if volatility calculation fails
                    recommended_leverage = "2-5x"

                message += f"\n*Futures Trading Guidance:*\n"
                message += f"• Recommended leverage: {recommended_leverage}\n"
                message += f"• Position size: 1-2% of your capital\n"
                message += f"• Set stop loss exactly as recommended\n"
                message += f"• Consider taking partial profits at each target\n"

            message += f"\nTo accept this trade, click the button below:"

            # Create unique signal ID
            signal_id = f"{best_trade['symbol'].replace('/', '_')}_{best_trade['direction']}_{datetime.now().timestamp()}"

            # Send the enhanced trade signal to the user
            try:
                # Extract trade details
                entry_price = best_trade.get("entry_price", 0)
                stop_loss = best_trade.get("stop_loss", 0)
                targets = best_trade.get("targets", [])

                # Determine market type
                market_type_str = market_type.upper()

                # Calculate volatility for enhanced signal
                try:
                    volatility = self.trading_strategy.calculate_volatility(best_trade['symbol'])
                except:
                    volatility = 0.03  # Default volatility

                # Extract leverage recommendation for futures
                leverage = None
                if market_type_str == "FUTURES":
                    if volatility < 0.02:  # Very low volatility (<2%)
                        leverage = "5-10x"
                    elif volatility < 0.04:  # Low volatility (2-4%)
                        leverage = "3-5x"
                    elif volatility < 0.06:  # Medium volatility (4-6%)
                        leverage = "2-3x"
                    else:  # High volatility (>6%)
                        leverage = "1-2x"

                # Create enhanced TradeSignal with detailed information
                explanations = best_trade.get("explanations", [])
                concepts = best_trade.get("concepts_met", [])

                # Create market structure analysis
                market_structure = ""
                if best_trade['direction'] == "BUY":
                    market_structure = "Bullish market structure confirmed with higher highs and higher lows pattern"
                else:
                    market_structure = "Bearish market structure confirmed with lower highs and lower lows pattern"

                # Create volume analysis
                volume_analysis = "Institutional volume patterns support the directional bias with smart money accumulation/distribution"

                # Create risk assessment
                risk_assessment = f"Premium SMC/ICT setup with {best_quality:.1%} confidence rating based on institutional concepts"

                enhanced_signal = TradeSignal(
                    symbol=best_trade['symbol'],
                    direction=best_trade['direction'],
                    entry_price=entry_price,
                    stop_loss=stop_loss,
                    targets=targets,
                    win_rate=0.90,
                    market_type=market_type_str,
                    technical_analysis=explanations,
                    concepts_met=concepts,
                    market_structure=market_structure,
                    volume_analysis=volume_analysis,
                    risk_assessment=risk_assessment,
                    quality_score=best_quality,
                    volatility=volatility,
                    leverage_recommendation=leverage or ""
                )

                # Create trade data
                trade_data = {
                    "signal_id": signal_id,
                    "symbol": best_trade['symbol'],
                    "direction": best_trade['direction'],
                    "entry_price": entry_price,
                    "stop_loss": stop_loss,
                    "targets": targets,
                    "leverage": leverage,
                    "market_type": market_type_str,
                    "quality_score": best_quality,
                    "user_id": user_id,
                    "timestamp": datetime.now().timestamp(),
                    "status": "waiting_entry",
                    "conditions": best_trade.get("conditions", {})
                }

                # Add trade to waiting trades
                from utils.shared_data_manager import add_waiting_trade
                add_waiting_trade(trade_data, market_type_str)

                # Record the trade for this user (counts against daily limit)
                from models.database import record_trade
                record_trade(user_id, trade_data)

                # Send enhanced signal message
                await bot.send_message(
                    chat_id=user_id,
                    text=enhanced_signal.format_message(),
                    parse_mode='HTML'  # Changed to HTML for better formatting
                )

                logger.info(f"Sent enhanced trade signal {signal_id} to user {user_id} and added to waiting trades")
            except Exception as e:
                logger.error(f"Error sending trade signal to user {user_id}: {e}")
        else:
            # No high-quality trade found - STRICT SMC/ICT ONLY
            logger.info(f"No premium SMC/ICT {market_type} trade found for user {user_id}")

            try:
                # No premium SMC/ICT trades found at all
                await bot.send_message(
                    chat_id=user_id,
                    text=f"❌ *NO PREMIUM {market_type} SETUPS AVAILABLE*\n\n"
                         f"📊 *Analysis Complete:* {len(filtered_pairs)} trading pairs analyzed\n\n"
                         f"🎯 *Our Standards:*\n"
                         f"• Minimum 3+ SMC/ICT concepts must align\n"
                         f"• Quality score must exceed 95%\n"
                         f"• Clear institutional footprints required\n"
                         f"• No medium-risk trades offered\n\n"
                         f"💡 *Professional Guidance:*\n"
                         f"• Elite traders wait for premium setups\n"
                         f"• Quality over quantity is our philosophy\n"
                         f"• Current market lacks institutional clarity\n"
                         f"• Patience is the highest trading skill\n\n"
                         f"⏰ *Next Steps:*\n"
                         f"• Try again in 1-2 hours\n"
                         f"• Market structure may improve\n"
                         f"• We never compromise on quality\n\n"
                         f"Use /trade again when you're ready to check for new premium opportunities.",
                    parse_mode='Markdown'
                )
            except Exception as e:
                logger.error(f"Error sending no-trade message to user {user_id}: {e}")

async def main():
    """Main function to run the trade checker."""
    # Create trade checker
    trade_checker = TradeChecker()

    # Set up signal handlers for graceful shutdown
    import signal

    def signal_handler(sig, frame):
        logger.info(f"Received signal {sig}, shutting down gracefully...")
        trade_checker.stop()
        logger.info("Trade checker stopped by signal")
        sys.exit(0)

    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # Termination signal

    try:
        # Start trade checker
        logger.info("Starting trade checker in automated mode")
        await trade_checker.start()
    except KeyboardInterrupt:
        # Stop trade checker on keyboard interrupt
        trade_checker.stop()
        logger.info("Trade checker stopped by user")
    except Exception as e:
        # Log any other exceptions
        logger.error(f"Error in trade checker: {e}")
        trade_checker.stop()

if __name__ == "__main__":
    # Run the trade checker
    asyncio.run(main())
